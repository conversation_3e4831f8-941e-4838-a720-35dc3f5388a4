using Klee.Web.App;
using DevExpress.Blazor;
using DevExpress.XtraCharts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Authorization;
using Renoir.Configs;
using Renoir.Srp.Portal.Web.Pages.Systems.SysEntityInfo.SysEntityInfoDetails.Services;
using Renoir.Web.Razor.Components.DetectPreRendering;
using Renoir.Web.Razor.Services.DialogBoxes;
using Renoir.Web.Razor.Services.UserNotifications;
using System.Diagnostics;
using Autofac;
using Klee.Domain.Services;
using Klee.Infrastructure.Application.Messages;
using Klee.Infrastructure.Helpers;
using Microsoft.Identity.Web;
using Renoir.Srp.Portal.Web.Helpers;
using DevExpress.CodeParser;
using Hangfire;
using Renoir.Srp.Portal.Web.Application.Security.Hangfire;
using Renoir.Srp.Portal.Web.Application.Security.Policies;
using Renoir.Application.Jobs.ReportJobs.Reports.Definitions.Services;
using Renoir.Application.Messages.General;
using Renoir.Application.Security;
using Renoir.Application.UserSessionStores;
using Renoir.Application.SoftwareEnvironments;
using Renoir.Application.Jobs.BackgroundJobs.JobRunners;
using Autofac.Core;
using Autofac.Extensions.DependencyInjection;
using Klee.Domain.Services.MessagingService;
using Klee.Domain.Services.QueryHandlers.VehicleManagement.Vehicles;
using Monet.MS.Logging;
using Microsoft.Extensions.DependencyInjection;
using Paramore.Brighter.Extensions.DependencyInjection;
using Paramore.Darker.AspNetCore;
using Klee.Infrastructure.Data;
using NLog.Web;
using QuestPDF.Infrastructure;
using ConfigurationKeys1 = Renoir.Configs.ConfigurationKeys;
using ConfigurationKeys2 = Klee.Web.App.Configs.ConfigurationKeys;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Klee.Domain.Services.UserContextService;
using Renoir.Srp.Portal.Web.Application.Security;

#pragma warning disable ASP0014

var builder = WebApplication.CreateBuilder(args);
builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());

// Configure QuestPDF license (Community license for evaluation)
QuestPDF.Settings.License = LicenseType.Community;

#region UI
{
    // Register/Configure AAD authentication
    builder.Services
        .AddMicrosoftIdentityWebAppAuthentication(builder.Configuration);
        //.EnableTokenAcquisitionToCallDownstreamApi()
        //.AddInMemoryTokenCaches();

    // ...
    builder.Services.AddRazorComponents()
        .AddInteractiveServerComponents()
        .AddInteractiveWebAssemblyComponents();

    builder.Services.AddHttpContextAccessor();
    builder.Services.AddControllersWithViews(options =>
    {
        var policy = new AuthorizationPolicyBuilder()
            .RequireAuthenticatedUser()
            .Build();
        options.Filters.Add(new AuthorizeFilter(policy));
    });

    //
    builder.Services.AddRazorPages();
    builder.Services.AddServerSideBlazor()
        .AddMicrosoftIdentityConsentHandler()
        .AddCircuitOptions(options => { options.DetailedErrors = true; })
        .AddHubOptions(options =>
        {
            // REMARK: Because of an Autofac issue
            //         https://stackoverflow.com/questions/76098236/blazor-inputfile-component-does-not-work-when-registering-autofacserviceprovider
            //         https://github.com/dotnet/aspnetcore/issues/38842#issuecomment-**********
            //         https://github.com/dotnet/aspnetcore/issues/47875
            options.DisableImplicitFromServicesParameters = true;
        });

    // Register extra authentication
    {
        builder.Services.AddSeafarRenoirServicesOfBasicAuthentication(builder.Configuration);
    }

    // Register DevExpress
    {
        builder.Services.AddDevExpressBlazor(options =>
        {
            options.SizeMode = SizeMode.Small;
            options.BootstrapVersion = BootstrapVersion.v5;
        });
    }

    // Register AntDesign
    {
        builder.Services.AddAntDesign();
    }

    // Register Google Analytics
    {
        builder.Services.AddGoogleAnalytics(builder.Configuration[ConfigurationKeys2.GoogleAnalytics_MeasurementId],
            debug: Debugger.IsAttached);
    }

    // Register Security
    {
        builder.Services.AddAuthorization(options => { options.AddSeafarRenoirPolicies(); });
    }

    // Register Seafar Services
    {
        builder.Services.AddSeafarServicesOfSrp(builder.Configuration);
        //builder.Services.AddSeafarServicesOfHangfireClient(builder.Configuration);
        //builder.Services.AddSeafarServicesOfHealthChecks(builder.Configuration);
    }
    //Register User context service
    {
        builder.Services.AddSingleton<IUserContextHelperService, UserContextHelperService>();
    }

    // Register User Authentication Service
    {
        builder.Services.AddScoped<Klee.Web.App.Services.Authentication.IUserAuthenticationService, Klee.Web.App.Services.Authentication.UserAuthenticationService>();
    }

    // Register PDF Generation Service
    {
        builder.Services.AddScoped<Klee.Domain.Services.PdfGeneration.IPdfGenerationService, Klee.Domain.Services.PdfGeneration.PdfGenerationService>();
    }
    // Register Extra Services UI
    {
        //builder.Services.AddScoped<IUserSessionService, UserSessionService>();
        builder.Services.AddScoped<IPreRenderFlag, PreRenderFlag>();
        builder.Services.AddScoped<IRDialogBoxService, RDialogBoxService>();
        builder.Services.AddScoped<IRUserNotificationService, RUserNotificationService>();
        builder.Services.AddScoped<ISysEntityInfoDetailsViewService, SysEntityInfoDetailsViewService>();
    }

    // Add JobRunner Services
    {
        // REMARK: Still register hangfire !!!!!!!!!!!!!!!
        builder.Services.AddScoped<IJobRunner, JobRunnerNull>();
        builder.Services.AddScoped<IBackgroundJobClient, BackgroundJobClientNull>();
    }

    // Add Logging
    {
        builder.Services.AddLogging(options =>
        {
            options.AddConfiguration(builder.Configuration.GetSection(ConfigurationKeys.Logging));
            options.ClearProviders();
            options.AddNLog("Configs/NLog.config");
            //options.AddSerilog();
            //options.AddConsole();
            //options.AddDebug();
            //options.AddEventSourceLogger();
        });
    }
}
#endregion


#region Infrastructure
{
    // Register Store (InMemory/Azure/...)
    {
        // UserSession (In Memory)
        builder.Services.AddScoped<IUserSessionStore, UserSessionInMemoryStore>();
    }

    // Register Extra Services
    {
        builder.Services.AddScoped<IMessageContextProvider, MessageContextProvider>();
        builder.Services.AddScoped<IAuthenticationState, AuthenticationState>();
        builder.Services.AddScoped<ISrpProcessors, SrpProcessors>();
        builder.Services.AddScoped<IReportTypeJobDefinitionQueryService, ReportTypeJobDefinitionQueryService>();
    }

    // Register Brighter
    {
        builder.Services.AddBrighter(options =>
            {
                options.CommandProcessorLifetime = ServiceLifetime.Transient;
                options.HandlerLifetime = ServiceLifetime.Transient;
            })
            .AsyncHandlersFromAssemblies(typeof(GetVehicleListQueryHandler).Assembly);
    }

    // Register Darker
    {
        builder.Services.AddDarker(options =>
            {
                options.QueryProcessorLifetime = ServiceLifetime.Transient;
                options.HandlerLifetime = ServiceLifetime.Transient;
            })
            .AddHandlersFromAssemblies(typeof(GetVehicleListQueryHandler).Assembly);
    }

    // Register Software Environment
    {
        SoftwareEnvironmentInfo softwareEnvironmentInfo = new SoftwareEnvironmentInfo(builder.Configuration);
        builder.Services.AddSingleton<ISoftwareEnvironmentInfo>(softwareEnvironmentInfo);
        builder.Services.AddSingleton<ISoftwareEnvironmentInfoExtended>(softwareEnvironmentInfo);
    }

    // Create & register Autofac container (service provider)
    {
        // Use Autofac as the DI container
        builder.Host.ConfigureContainer<ContainerBuilder>(containerBuilder =>
        {
            // Additional Autofac registrations
            containerBuilder.AddSeafarServicesOfSrpAutofacModules();
        });
    }

    //Register twilio
    {
        //SMS
        builder.Services.Configure<TwilioSettings>(builder.Configuration.GetSection("Twilio"));
        builder.Services.AddScoped<ISmsService, TwilioSmsService>();

        //EMAIL
        builder.Services.Configure<SendGridSettings>(builder.Configuration.GetSection("SendGrid"));
        builder.Services.AddScoped<IEmailService, SendGridEmailService>();
        builder.Services.AddScoped<IVoyageNotificationService, VoyageNotificationService>();
    }
}
#endregion

var app = builder.Build();

// Init Logging (Monet)
AppMsLogging.LoggerFactory = app.Services.GetService<ILoggerFactory>();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers();

    endpoints.MapBlazorHub(options =>
    {
        //options.Transports = HttpTransportType.WebSockets;
        //HttpTransportType.LongPolling;
        //options.WebSockets.CloseTimeout = TimeSpan.FromSeconds(15);
    });
    endpoints.MapRazorPages();
    endpoints.MapFallbackToPage("/_Host");
});

//app.MapRazorComponents<App>()
//    .AddInteractiveServerRenderMode()
//    .AddInteractiveWebAssemblyRenderMode()
//    .AddAdditionalAssemblies(typeof(Klee.Web.App.Client._Imports).Assembly);

// Ensure database is created for AppSrpDbContext (Cosmos DB)
using (var scope = ((IApplicationBuilder)app).ApplicationServices.GetRequiredService<IServiceScopeFactory>()
       .CreateScope())
using (var context = scope.ServiceProvider.GetService<AppSrpDbContext>())
{
    context.Database.EnsureCreated();
}

app.Run();