﻿@page "/"
@using Klee.Web.App
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Mvc.TagHelpers
@namespace Renoir.Srp.Portal.Web.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!doctype html>
<html class="fixed has-top-menu has-left-sidebar-half h-full">
@*sidebar-left-collapsed*@
<head>

    <!-- Basic -->
    <title>SEAFAR | RP</title>

    <base href="~/"/>

    <meta charset="UTF-8">
    <meta name="keywords" content="SEAFAR"/>
    <meta name="description" content="SEAFAR portal">
    <meta name="author" content="seafar.eu">
    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
    
    @(await Html.PartialAsync("~/Shared/Hosting/RHostHtmlHead.cshtml"))
    
    <link rel="stylesheet" href="Styles/App-dist.css" />
    <link href="_content/AntDesign/css/ant-design-blazor.css" rel="stylesheet" />
    
</head>
<body class="h-full">
    <component type="typeof(App)" render-mode="ServerPrerendered" />

    @(await Html.PartialAsync("~/Shared/Hosting/RHostHtmlBodyPost.cshtml"))

    <script src="_framework/blazor.server.js"></script>
    <script src="_content/AntDesign/js/ant-design-blazor.js"></script>
    <script src="~/js/fileDownload.js"></script>
    <script>
        !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init Ee Ps Rs xe ks Is capture We calculateEventProperties Cs register register_once register_for_session unregister unregister_for_session Ds getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Fs Ms createPersonProfile As Es opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Ts debug Os getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
        posthog.init('phc_mZnTKQ61eKDHS00bHFFeS58vm7XmNZLKKY8MKTLK7VF', {
            api_host: 'https://eu.i.posthog.com',
            defaults: '2025-05-24',
            person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
        })
    </script>
</body>
</html>