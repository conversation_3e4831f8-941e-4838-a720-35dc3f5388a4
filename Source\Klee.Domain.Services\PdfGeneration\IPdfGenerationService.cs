using System.Threading.Tasks;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;

namespace Klee.Domain.Services.PdfGeneration;

/// <summary>
/// Service for generating PDF documents
/// </summary>
public interface IPdfGenerationService
{
    /// <summary>
    /// Generates a PDF invoice document from invoice data
    /// </summary>
    /// <param name="invoice">The invoice data to generate PDF for</param>
    /// <returns>PDF document as byte array</returns>
    Task<byte[]> GenerateInvoicePdfAsync(InvoiceListItem invoice);
}
