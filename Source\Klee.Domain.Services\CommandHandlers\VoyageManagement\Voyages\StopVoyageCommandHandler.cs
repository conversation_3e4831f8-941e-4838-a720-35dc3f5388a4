using System;
using System.Threading;
using System.Threading.Tasks;
using Klee.Domain.Entities.InvoiceManagement.Invoices;
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Klee.Domain.Entities.VoyageManagement.Voyages.Data;
using Klee.Domain.Messages.Commands.VoyageManagement.Voyages;
using Klee.Domain.Services.Repositories.InvoiceManagement;
using Klee.Domain.Services.Repositories.VoyageManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Paramore.Brighter;
using Renoir.Application.EF.Data.Domains.Srp;
using Renoir.Application.Messages.Commands.CommandHandlers.RequestContexts;

namespace Klee.Domain.Services.CommandHandlers.VoyageManagement.Voyages
{
    public sealed class StopVoyageCommandHandler
        : RequestHandlerAsync<StopVoyageCommand>
    {
        #region PROPERTIES
        private IVoyageSrpRepository VoyageSrpRepository { get; }
        private IVoyageInvoiceSrpRepository VoyageInvoiceSrpRepository { get; }
        private IAppSrpDbContext DbContext { get; }
        private ILogger<StopVoyageCommandHandler> Logger { get; }
        #endregion

        #region CONSTRUCTORS
        public StopVoyageCommandHandler(IVoyageSrpRepository voyageSrpRepository,
                                       IVoyageInvoiceSrpRepository voyageInvoiceSrpRepository,
                                       IAppSrpDbContext dbContext,
                                       ILogger<StopVoyageCommandHandler> logger)
        {
            VoyageSrpRepository = voyageSrpRepository;
            VoyageInvoiceSrpRepository = voyageInvoiceSrpRepository;
            DbContext = dbContext;
            Logger = logger;
        }
        #endregion

        #region METHODS
        [RequestContextCommandHandler(step: 1, HandlerTiming.Before)]
        public override async Task<StopVoyageCommand> HandleAsync(StopVoyageCommand command,
            CancellationToken cancellationToken = new())
        {
            // Use database transaction to ensure atomicity
            await using var transaction = await DbContext.Database.BeginTransactionAsync(cancellationToken);

            try
            {
                // Find the voyage with related data
                Voyage voyage = await DbContext.Set<Voyage>()
                    .Include(v => v.Vehicle)
                    .Include(v => v.Operator)
                    .FirstOrDefaultAsync(v => v.VoyageId == command.VoyageId &&
                                             v.EntityPartitionKey == command.VoyageId.ToString(), cancellationToken);

                if (voyage == null)
                {
                    throw new InvalidOperationException($"Voyage with ID {command.VoyageId} not found.");
                }

                // Verify voyage is in Executing status
                if (voyage.Status != VoyageStatus.Executing)
                {
                    throw new InvalidOperationException($"Voyage must be in 'Executing' status to stop. Current status: {voyage.Status}");
                }

                // Verify voyage has an operator assigned
                if (string.IsNullOrEmpty(voyage.OperatorId) || voyage.Operator == null)
                {
                    throw new InvalidOperationException("Cannot complete voyage without an assigned operator.");
                }

                // Update voyage status to Completed
                voyage.Status = VoyageStatus.Completed;
                await VoyageSrpRepository.UpdateAsync(voyage, command);

                // Create invoice for the completed voyage
                var voyageDurationHours = (voyage.EndDateTime - voyage.StartDateTime).TotalHours;
                var operatorCost = voyageDurationHours * voyage.Operator.HourlyRateInEuros;
                var commissionRate = 0.05; // 5%
                var commissionAmount = operatorCost * commissionRate;
                var totalCost = operatorCost + commissionAmount;

                VoyageInvoice voyageInvoice = new VoyageInvoice()
                {
                    BookingOrganizationId = voyage.BookingOrganizationId,
                    OperatorOrganizationId = voyage.Operator.OrganizationId,
                    VoyageId = voyage.VoyageId,
                    TotalAmountInEuros = totalCost,
                    OperatorCostInEuros = operatorCost,
                    CommissionAmountInEuros = commissionAmount,
                    Status = VoyageInvoiceStatus.Open,
                    PaymentDate = null
                };

                await VoyageInvoiceSrpRepository.AddAsync(voyageInvoice, command);

                // Commit transaction
                await transaction.CommitAsync(cancellationToken);

                // Set result
                command.Result.VoyageId = voyage.VoyageId;
                command.Result.VoyageInvoiceId = voyageInvoice.VoyageInvoiceId;
                command.Result.Success = true;

                Logger.LogInformation($"Voyage {command.VoyageId} completed successfully and invoice {voyageInvoice.VoyageInvoiceId} created");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync(cancellationToken);
                Logger.LogError(ex, $"Error stopping voyage {command.VoyageId}");
                throw;
            }

            return await base.HandleAsync(command, cancellationToken).ConfigureAwait(ContinueOnCapturedContext);
        }
        #endregion
    }
}
