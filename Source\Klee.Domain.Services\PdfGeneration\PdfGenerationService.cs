using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;
using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data;
using Klee.Domain.Messages.Queries.VoyageManagement.Voyages;
using Klee.Domain.Services.PdfGeneration.Documents;
using Klee.Domain.Services.PdfGeneration.Models;
using Klee.Domain.Services.Repositories.InvoiceManagement;
using Microsoft.EntityFrameworkCore;
using QuestPDF.Fluent;
using Renoir.Application.Messages.Queries;

namespace Klee.Domain.Services.PdfGeneration;

/// <summary>
/// Service implementation for generating PDF documents
/// </summary>
public class PdfGenerationService : IPdfGenerationService
{
    #region FIELDS
    private readonly IVoyageInvoiceSrpRepository _voyageInvoiceRepository;
    private readonly ISrpProcessors _srpProcessors;
    #endregion

    #region CONSTRUCTORS
    public PdfGenerationService(
        IVoyageInvoiceSrpRepository voyageInvoiceRepository,
        ISrpProcessors srpProcessors)
    {
        _voyageInvoiceRepository = voyageInvoiceRepository;
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS
    public async Task<byte[]> GenerateInvoicePdfAsync(InvoiceListItem invoice)
    {
        // Get query context from SrpProcessors
        var queryContext = await _srpProcessors.GetQueryContextAsync();
        GetVoyageManagementListQuery query = new GetVoyageManagementListQuery(queryContext);
        // Get detailed invoice data with related entities
        var detailedInvoice = await _voyageInvoiceRepository.Entities(query)
            .Where(vi => vi.VoyageInvoiceId == invoice.VoyageInvoiceId)
            .Include(vi => vi.BookingOrganization)
            .Include(vi => vi.OperatorOrganization)
            .Include(vi => vi.Voyage)
                .ThenInclude(v => v.Vehicle)
            .Include(vi => vi.Voyage)
                .ThenInclude(v => v.Operator)
            .FirstOrDefaultAsync();

        if (detailedInvoice == null)
            throw new InvalidOperationException($"Invoice with ID {invoice.VoyageInvoiceId} not found");

        // Calculate price breakdown
        var voyageDurationHours = (detailedInvoice.Voyage.EndDateTime - detailedInvoice.Voyage.StartDateTime).TotalHours;
        var operatorCost = voyageDurationHours * detailedInvoice.Voyage.Operator.HourlyRateInEuros;
        var commissionRate = 0.05; // 5%
        var commissionAmount = operatorCost * commissionRate;

        // Map to PDF model
        var pdfModel = new InvoicePdfModel
        {
            VoyageInvoiceId = detailedInvoice.VoyageInvoiceId,
            InvoiceNumber = $"INV-RP-{detailedInvoice.VoyageInvoiceId.ToString("N")[..8].ToUpper()}",
            CreatedDate = detailedInvoice.CreatedDateTimeUtc,
            PaymentDate = detailedInvoice.PaymentDate,
            Status = detailedInvoice.Status,
            TotalAmountInEuros = detailedInvoice.TotalAmountInEuros,

            // Price breakdown
            VoyageDurationHours = voyageDurationHours,
            OperatorCost = operatorCost,
            CommissionRate = commissionRate,
            CommissionAmount = commissionAmount,

            BookingOrganization = new InvoicePdfModel.OrganizationInfo
            {
                Name = detailedInvoice.BookingOrganization?.Name ?? "Unknown Organization",
                Code = detailedInvoice.BookingOrganization?.Code ?? "",
                ContactEmail = detailedInvoice.BookingOrganization?.ContactEmail ?? "",
                ContactPhone = detailedInvoice.BookingOrganization?.ContactPhone ?? "",
                Address = detailedInvoice.BookingOrganization?.Address ?? "",
                City = "", // Organization entity doesn't have City property
                Country = "" // Organization entity doesn't have Country property
            },

            OperatorOrganization = new InvoicePdfModel.OrganizationInfo
            {
                Name = detailedInvoice.OperatorOrganization?.Name ?? "Unknown Organization",
                Code = detailedInvoice.OperatorOrganization?.Code ?? "",
                ContactEmail = detailedInvoice.OperatorOrganization?.ContactEmail ?? "",
                ContactPhone = detailedInvoice.OperatorOrganization?.ContactPhone ?? "",
                Address = detailedInvoice.OperatorOrganization?.Address ?? "",
                City = "", // Organization entity doesn't have City property
                Country = "" // Organization entity doesn't have Country property
            },

            SeafarInfo = new InvoicePdfModel.SeafarCompanyInfo(),

            Voyage = new InvoicePdfModel.VoyageInfo
            {
                VesselName = detailedInvoice.Voyage?.Vehicle?.VehicleName ?? "Unknown Vessel",
                VesselType = detailedInvoice.Voyage?.Vehicle?.VehicleTypeId.ToString() ?? "Unknown Type",
                StartDateTime = detailedInvoice.Voyage?.StartDateTime ?? DateTime.MinValue,
                EndDateTime = detailedInvoice.Voyage?.EndDateTime ?? DateTime.MinValue,
                Description = detailedInvoice.Voyage?.Description ?? "",
                OperatorName = detailedInvoice.Voyage?.Operator != null 
                    ? $"{detailedInvoice.Voyage.Operator.FirstName} {detailedInvoice.Voyage.Operator.LastName}".Trim()
                    : "No Operator Assigned",
                Status = detailedInvoice.Voyage?.Status ?? Klee.Domain.Entities.VoyageManagement.Voyages.Data.VoyageStatus.Open
            }
        };

        // Generate PDF
        var document = new InvoicePdfDocument(pdfModel);
        return document.GeneratePdf();
    }
    #endregion


}
