﻿using Klee.Web.App.Pages.VehicleManagement.Vehicles.VehicleList;
using Klee.Web.App.Services.Authentication;
using Klee.Web.App.Components.Pages;
using Microsoft.AspNetCore.Components;
using Renoir.Application.Security.Helpers;
using Renoir.Srp.Portal.Web.Pages;
using Renoir.Srp.Portal.Web.Pages.Common;

namespace Klee.Web.App.Pages
{
    public class IndexViewBase : LayoutBodyViewBase<IndexViewModel>
    {
        
        #region METHODS - OVERRIDE
        protected override async Task OnInitializedAsync()
        {
            // Init
            this.ViewModel = new IndexViewModel(this);

            //
            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            try
            {
                //
                if (!AuthenticatedUser.IsAdminApp() && !AuthenticatedUser.IsAdminSystem())
                {
                    // Redirect non-admin users to dashboard
                    this.NavigationManager.NavigateTo(Dashboard.GetUri());
                    return;
                }
                await base.OnParametersSetAsync();
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when setting parameters.");
                #endregion

                //throw;
            }
        }
        #endregion

        #region EVENT HANDLERS
        protected Task OnClick_ViewMainDashboard()
        {
            // Init
            string userId = this.AuthenticatedUserId ?? "";

            try
            {
                // View UserVehicles
                this.NavigationManager.NavigateTo(Dashboard.GetUri());
            }
            catch (Exception exception)
            {
                #region Logging
                this.Logger.LogError(exception, "Exception when navigating to weather.", userId);
                #endregion

                //throw;
            }

            return Task.CompletedTask;
        }
        #endregion
    }
}