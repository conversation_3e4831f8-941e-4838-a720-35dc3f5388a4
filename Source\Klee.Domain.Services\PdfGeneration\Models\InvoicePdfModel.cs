using System;
using Klee.Domain.Entities.InvoiceManagement.Invoices.Data;
using Klee.Domain.Entities.VoyageManagement.Voyages.Data;

namespace Klee.Domain.Services.PdfGeneration.Models;

/// <summary>
/// Model containing all data needed for invoice PDF generation
/// </summary>
public class InvoicePdfModel
{
    #region PROPERTIES - INVOICE DETAILS
    public Guid VoyageInvoiceId { get; set; }
    public string InvoiceNumber { get; set; } = "";
    public DateTime CreatedDate { get; set; }
    public DateTime? PaymentDate { get; set; }
    public VoyageInvoiceStatus Status { get; set; }
    public double TotalAmountInEuros { get; set; }
    #endregion

    #region PROPERTIES - ORGANIZATIONS
    public OrganizationInfo BookingOrganization { get; set; } = new();
    public OrganizationInfo OperatorOrganization { get; set; } = new();
    public SeafarCompanyInfo SeafarInfo { get; set; } = new();
    #endregion

    #region PROPERTIES - VOYAGE DETAILS
    public VoyageInfo Voyage { get; set; } = new();
    #endregion

    #region PROPERTIES - PRICE BREAKDOWN
    public double VoyageDurationHours { get; set; }
    public double OperatorCost { get; set; }
    public double CommissionRate { get; set; }
    public double CommissionAmount { get; set; }
    #endregion

    #region NESTED CLASSES
    public class OrganizationInfo
    {
        public string Name { get; set; } = "";
        public string Code { get; set; } = "";
        public string ContactEmail { get; set; } = "";
        public string ContactPhone { get; set; } = "";
        public string Address { get; set; } = "";
        public string City { get; set; } = "";
        public string Country { get; set; } = "";
    }

    public class SeafarCompanyInfo
    {
        public string Name { get; set; } = "SEAFAR";
        public string Address { get; set; } = "Schaliënstraat 3, 2000 Antwerpen";
        public string Iban { get; set; } = "123";
        public string LogoPath { get; set; } = "/Logo/logo.png";
    }

    public class VoyageInfo
    {
        public string VesselName { get; set; } = "";
        public string VesselType { get; set; } = "";
        public DateTime StartDateTime { get; set; }
        public DateTime EndDateTime { get; set; }
        public string Description { get; set; } = "";
        public string OperatorName { get; set; } = "";
        public VoyageStatus Status { get; set; }
        
        public TimeSpan Duration => EndDateTime - StartDateTime;
        public string DurationDisplay
        {
            get
            {
                if (Duration.TotalDays >= 1)
                    return $"{(int)Duration.TotalDays}d {Duration.Hours}h {Duration.Minutes}m";
                return $"{Duration.Hours}h {Duration.Minutes}m";
            }
        }
        public string DateRangeDisplay => $"{StartDateTime.ToLocalTime():MMM dd, yyyy HH:mm} - {EndDateTime.ToLocalTime():MMM dd, yyyy HH:mm}";
    }
    #endregion
}
