﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	<TargetFramework>net8.0</TargetFramework>
	<LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
	  <PackageReference Include="Autofac" Version="8.2.0" />
	  <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
    <PackageReference Include="Paramore.Darker" Version="4.0.1" />
    <PackageReference Include="QuestPDF" Version="2025.4.4" />
    <PackageReference Include="Renoir.Application.Core" Version="1.1.25078.1-beta" />
    <PackageReference Include="Renoir.Application.EF.Core" Version="1.1.25078.1-beta" />
    <PackageReference Include="Renoir.Application.Messages.Core" Version="1.1.25078.1-beta" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="Twilio" Version="7.11.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Klee.Domain.Entities\Klee.Domain.Entities.csproj" />
    <ProjectReference Include="..\Klee.Domain.Messages\Klee.Domain.Messages.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="CommandHandlers\OrganizationVehicleMangement\OrganizationVehicles\" />
    <Folder Include="CommandHandlers\StationManagement\Stations\Helpers\" />
    <Folder Include="CommandHandlers\UserManagement\Users\Helpers\" />
    <Folder Include="QueryHandlers\OperatorManagement\Operators\" />
    <Folder Include="QueryHandlers\OrganizationOperatorManagement\OrganizationOperators\" />
    <Folder Include="QueryHandlers\OrganizationRocManagement\OrganizationRocs\" />
    <Folder Include="QueryHandlers\StationManagement\Stations\" />
    <Folder Include="Repositories\StationManagement\" />
    <Folder Include="Repositories\RocManagement\" />
    <Folder Include="Repositories\UserManagement\Users\" />
  </ItemGroup>

</Project>
