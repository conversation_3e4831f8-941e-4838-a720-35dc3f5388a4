@page "/invoices"

@using Klee.Domain.Entities.InvoiceManagement.Invoices.Data
@using Klee.Domain.Entities.VoyageManagement.Voyages.Data
@using Klee.Domain.Messages.Queries.InvoiceManagement.Invoices.Data
@layout OrganizationViewLayout

@inherits InvoicesViewBase

<!-- Header -->
<div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-bold text-teal-700">Stats</h1>
</div>

@if (ViewModel.IsLoading == true)
{
    <div class="text-center py-12">
        <Spin Size="SpinSize.Large" />
        <p class="mt-4 text-gray-600">Loading invoices...</p>
    </div>
}
else
{
    <!-- Dashboard Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Voyages Posted This Month -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-ship text-4xl text-teal-700"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Voyages Posted</p>
                        <p class="text-2xl font-bold text-gray-900">@ViewModel.Statistics.VoyagesPostedThisMonth</p>
                        <p class="text-xs text-gray-500">
                            @if (ViewModel.Statistics.VoyagesPostedThisMonth > 0)
                            {
                                <span>(@ViewModel.Statistics.VoyagesBookedThisMonth booked, @ViewModel.GetBookingConversionRate() rate) • This month</span>
                            }
                            else
                            {
                                <span>This month</span>
                            }
                        </p>
                    </div>
                </div>
            </div>
        </Card>

        <!-- Total Hours This Month -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-4xl text-teal-700"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Hours</p>
                        <p class="text-2xl font-bold text-gray-900">@ViewModel.Statistics.TotalHoursThisMonth.ToString("F1")</p>
                        <p class="text-xs text-gray-500">Completed voyages • This month</p>
                    </div>
                </div>
            </div>
        </Card>

        <!-- Money Earned This Month -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-euro-sign text-4xl text-teal-700"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Earned This Month</p>
                        <p class="text-2xl font-bold text-gray-900">@ViewModel.FormatCurrency(ViewModel.Statistics.MoneyEarnedThisMonth)</p>
                        <p class="text-xs text-gray-500">Incoming invoices</p>
                    </div>
                </div>
            </div>
        </Card>

        <!-- Total Money Earned -->
        <Card Class="@TailwindStyleStrings.Card.Container">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-4xl text-teal-700"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Earned</p>
                        <p class="text-2xl font-bold text-gray-900">@ViewModel.FormatCurrency(ViewModel.Statistics.TotalMoneyEarned)</p>
                        <p class="text-xs text-gray-500">All time</p>
                    </div>
                </div>
            </div>
        </Card> 
    </div> 

    <!-- Invoice Table -->
    <div class="flex items-center justify-between mb-4">
        <h1 class="text-2xl font-bold text-teal-700">Invoices</h1>
    </div>

    <Card Class="@TailwindStyleStrings.Card.Container">
        <div class="overflow-x-auto">
            <Table TItem="InvoiceListItem"
                   DataSource="@ViewModel.Invoices"
                   Class="@TailwindStyleStrings.Table.Container"
                   ExpandIconColumnIndex="0"
                   RowExpandable="@(record => true)"
                   ScrollX="1040">
                <ColumnDefinitions>
                <PropertyColumn Property="c => c.VesselName" Title="Vessel" Width="200" Sortable Filterable>
                    <Template>
                        <div class="flex items-center gap-2">
                            <i class="@($"fas fa-ship h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                            <span class="font-medium">@context.VesselName</span>
                        </div>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.Duration" Title="Duration" Sortable Width="120">
                    <Template>
                        <span class="text-sm">@ViewModel.FormatDuration(context.Duration)</span>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.OperatorFullName" Title="Operator" Sortable Filterable Width="180">
                    <Template>
                        <div class="flex items-center gap-2">
                            <i class="@($"fas fa-user h-4 w-4 {TailwindStyleStrings.Icon.Default}")"></i>
                            <span>@context.OperatorFullName</span>
                        </div>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.OperatorOrganizationName" Title="Operator Organization" Filterable Width="200" />

                <PropertyColumn Property="c => c.Status" Title="Status" Sortable Width="100">
                    <Template>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border @ViewModel.GetStatusBadgeClass(context.Status)">
                            @context.Status.ToString()
                        </span>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.TotalAmountInEuros" Title="Amount" Sortable Width="120">
                    <Template>
                        <span class="font-medium">@ViewModel.FormatCurrency(context.TotalAmountInEuros)</span>
                    </Template>
                </PropertyColumn>

                <PropertyColumn Property="c => c.CreatedDate" Title="Direction" Width="120">
                    <Template>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border @ViewModel.GetDirectionBadgeClass(context)">
                            @ViewModel.GetInvoiceDirection(context)
                        </span>
                    </Template>
                </PropertyColumn>

                <ActionColumn Title="Actions" Width="180">
                    <Template>
                        <Space>
                            <!-- PDF Download Action -->
                            <SpaceItem>
                                <Button Type="@ButtonType.Default"
                                        Class="@TailwindStyleStrings.Button.Ghost"
                                        Size="@ButtonSize.Small"
                                        Icon="@IconType.Outline.Download"
                                        OnClick="() => DownloadInvoicePdf(context.VoyageInvoiceId)"
                                        Loading="@ViewModel.IsGeneratingPdf">
                                </Button>
                            </SpaceItem>

                            <!-- Payment Status Action -->
                            <SpaceItem>
                                @if (context.Status == VoyageInvoiceStatus.Open)
                                {
                                    @if (context.IsIncoming(ViewModel.CurrentUserOrganizationId))
                                    {
                                        <!-- Only operator organization can mark as paid (incoming invoices) -->
                                        <Button Type="@ButtonType.Default"
                                                Class="@TailwindStyleStrings.Button.Outline"
                                                Size="@ButtonSize.Small"
                                                OnClick="() => ShowMarkAsPaidConfirmation(context.VoyageInvoiceId, context.VesselName)"
                                                Loading="@ViewModel.IsUpdatingStatus">
                                            <i class="fas fa-check mr-1"></i>
                                            Mark as Paid
                                        </Button>
                                    }
                                    else
                                    {
                                        <!-- Booking organization cannot mark as paid (outgoing invoices) -->
                                        <span class="text-xs text-gray-600">
                                            <i class="fas fa-clock mr-1"></i>
                                            Awaiting Payment
                                        </span>
                                    }
                                }
                                else
                                {
                                    <span class="text-xs text-gray-500">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Paid @context.PaymentDate?.ToString("MMM dd")
                                    </span>
                                }
                            </SpaceItem>
                        </Space>
                    </Template>
                </ActionColumn>
                </ColumnDefinitions>
                <ExpandTemplate Context="invoice">
                    <div class="px-8 py-6 bg-gray-50">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Voyage Details -->
                            <div class="space-y-4">
                                <h4 class="font-semibold text-teal-700 border-b-2 border-teal-200 pb-3 mb-4">Voyage Details</h4>
                                <div class="bg-white rounded-lg p-4 shadow-sm">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Vessel</p>
                                            <p class="text-sm text-gray-900">@invoice.Data.VesselName</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Vessel Type</p>
                                            <p class="text-sm text-gray-900">@invoice.Data.VesselType</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Start Time</p>
                                            <p class="text-sm text-gray-900">@invoice.Data.StartDateTime.ToLocalTime().ToString("MMM dd, yyyy HH:mm")</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">End Time</p>
                                            <p class="text-sm text-gray-900">@invoice.Data.EndDateTime.ToLocalTime().ToString("MMM dd, yyyy HH:mm")</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Duration</p>
                                            <p class="text-sm text-gray-900">@invoice.Data.DurationDisplay</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Status</p>
                                            <div class="flex items-center gap-2">
                                                @switch (invoice.Data.VoyageStatus)
                                                {
                                                    case VoyageStatus.Open:
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200">
                                                            <i class="fas fa-clock mr-1"></i>
                                                            Open
                                                        </span>
                                                        break;
                                                    case VoyageStatus.Booked:
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                                                            <i class="fas fa-check mr-1"></i>
                                                            Booked
                                                        </span>
                                                        break;
                                                    case VoyageStatus.Executing:
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                                                            <i class="fas fa-play mr-1"></i>
                                                            Executing
                                                        </span>
                                                        break;
                                                    case VoyageStatus.Completed:
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                                            <i class="fas fa-flag-checkered mr-1"></i>
                                                            Completed
                                                        </span>
                                                        break;
                                                }
                                            </div>
                                        </div>
                                        @if (!string.IsNullOrWhiteSpace(invoice.Data.VoyageDescription))
                                        {
                                            <div class="md:col-span-2">
                                                <p class="text-sm font-medium text-teal-700 mb-1">Description</p>
                                                <p class="text-sm text-gray-900 leading-relaxed">@invoice.Data.VoyageDescription</p>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- Invoice Details -->
                            <div class="space-y-4">
                                <h4 class="font-semibold text-teal-700 border-b-2 border-teal-200 pb-3 mb-4">Invoice Details</h4>
                                <div class="bg-white rounded-lg p-4 shadow-sm">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Operator</p>
                                            <p class="text-sm text-gray-900">@invoice.Data.OperatorFullName</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Operator Organization</p>
                                            <p class="text-sm text-gray-900">@invoice.Data.OperatorOrganizationName</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Booking Organization</p>
                                            <p class="text-sm text-gray-900">@invoice.Data.BookingOrganizationName</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Direction</p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border @ViewModel.GetDirectionBadgeClass(invoice.Data)">
                                                @ViewModel.GetInvoiceDirection(invoice.Data)
                                            </span>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Total Amount</p>
                                            <p class="text-sm text-gray-900 font-semibold">@ViewModel.FormatCurrency(invoice.Data.TotalAmountInEuros)</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-teal-700 mb-1">Payment Status</p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border @ViewModel.GetStatusBadgeClass(invoice.Data.Status)">
                                                @invoice.Data.Status.ToString()
                                            </span>
                                        </div>
                                        @if (invoice.Data.PaymentDate.HasValue)
                                        {
                                            <div class="md:col-span-2">
                                                <p class="text-sm font-medium text-teal-700 mb-1">Payment Date</p>
                                                <p class="text-sm text-gray-900">@invoice.Data.PaymentDate.Value.ToString("MMM dd, yyyy")</p>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ExpandTemplate>
            </Table>
        </div>
    </Card> 
}

<!-- Mark as Paid Confirmation Modal -->
 <Modal Title="Confirm Payment"
       @bind-Visible="ShowMarkAsPaidModal"
       OnOk="ConfirmMarkAsPaid"
       OnCancel="CancelMarkAsPaid"
       OkText=@("Mark as Paid")
       CancelText=@("Cancel")>
    <p>@SelectedInvoiceDescription</p>
    <p class="text-sm text-gray-600 mt-2">This action cannot be undone.</p>
</Modal> 

@code {

    public static string GetUri()
    {
        return "/invoices";
    }

}  