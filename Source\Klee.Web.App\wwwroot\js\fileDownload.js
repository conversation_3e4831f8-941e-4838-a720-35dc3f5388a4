/**
 * File download utilities for Blazor Server applications
 * Provides functions to download files using blob URLs and JavaScript interop
 */

window.fileDownloadFunctions = {
    /**
     * Downloads a file from a byte array using blob URL
     * @param {string} filename - The name of the file to download
     * @param {string} contentType - The MIME type of the file
     * @param {Uint8Array} fileData - The file data as a byte array
     */
    downloadFromByteArray: function (filename, contentType, fileData) {
        try {
            // Create a blob from the byte array
            const blob = new Blob([fileData], { type: contentType });
            
            // Create a temporary URL for the blob
            const url = URL.createObjectURL(blob);
            
            // Create a temporary anchor element for download
            const anchor = document.createElement('a');
            anchor.href = url;
            anchor.download = filename;
            anchor.style.display = 'none';
            
            // Add to DOM, click, and remove
            document.body.appendChild(anchor);
            anchor.click();
            document.body.removeChild(anchor);
            
            // Clean up the blob URL
            URL.revokeObjectURL(url);
            
            return true;
        } catch (error) {
            console.error('Error downloading file:', error);
            return false;
        }
    },

    /**
     * Downloads a file from a base64 string
     * @param {string} filename - The name of the file to download
     * @param {string} contentType - The MIME type of the file
     * @param {string} base64Data - The file data as a base64 string
     */
    downloadFromBase64: function (filename, contentType, base64Data) {
        try {
            // Convert base64 to byte array
            const byteCharacters = atob(base64Data);
            const byteNumbers = new Array(byteCharacters.length);
            
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            
            const byteArray = new Uint8Array(byteNumbers);
            
            // Use the byte array download function
            return this.downloadFromByteArray(filename, contentType, byteArray);
        } catch (error) {
            console.error('Error downloading file from base64:', error);
            return false;
        }
    }
};
